- 电话：18015528893
- 邮箱：<EMAIL>
- 地址：北京朝阳区
- 方向：AIGC 应用
- 状态：在职（看机会）

## 教育背景
2014.9 - 2018.6 大连理工大学 工商管理 本科

## 技能评价
- 擅长设计 AIGC 产品（偏创作）及 AI 工程架构，具备从零开始构建内容创作架构的能力，已成功落地长/短篇小说的 AI 规模化创作产线。
- 熟悉内容创作/获取、内容处理和内容分发全链路，具有成功落地大型爬虫系统的经验，了解内容管线、内容池和推荐系统。
- 对内容和数据高度敏感，擅长结合内容理解与数据表现进行深度分析，擅长基于数据+内容的理解驱动产品优化。
- 熟练掌握 Python 和 Go 语言，具备扎实的编程基础，能够高效拆解复杂业务模块，合理设计系统架构层次。
- 具备微服务化，可观测体系建设，容灾体系建设和研发效能提升相关项目的落地经验，工程架构能力扎实。

## 工作经历
### 2023.6 - 至今 喜马拉雅 AI 原创内容产品事业部 网文负责人 2023 S / 2024 H1 S
- 探索各文字类内容 AI 创作的可能性。提出通过深入理解内容商业化创作逻辑，结合 AI、数据等技术进行创作流程的 AI 化改造，以实现自动化、规模化生产的可行路线。目前已在长篇网文、短篇网文中成功落地，儿童、播客业务探索中。
- 从0到1组建和带领近20人的跨领域团队，包括网文编辑、产品和 AI 工程师。构建长篇网文内容创作的理论体系，结合数据推荐、prompt 流、工具来重构传统的网文创作流程，并在6个月内实现了规模化生产链路的落地。全流程 AI 参与率 90%+，产出网文的数据表现达主流平台80-95分位。
- 针对短篇网文市场现状，快速制定出改编创作的方案。在1个月内独立探索出古改今、今改古、性转、架空等创作形式，并实现了创作流程的工程化，产出内容已在渠道和站内验证成功。
- 通过深入挖掘主流社交平台的话题、人群特征，探索各人群的注意力和情绪焦点，为内容创作提供全流程的指导，确保内容创作的时效和吸引力。

### 2021.5 - 2023.5 腾讯视频 体育平台研发中心 高级后台开发 2021 H2 4⭐️ / 2023 H1 5⭐️ / 2023 H2 5⭐
- 持续迭代升级赛事和内容相关的核心服务，包括图文直播、评论/弹幕、内容池、推荐接入层等。
- 负责体育后台的微服务化改造，由单体 PHP 服务拆分为 API 网关、接口适配层和微服务层，大幅提升架构性能、可观测性和可维护性。
- 从0构建体育的降级容灾能力，包括限流、降级缓存和全链路过载保护，提高资源利用率的同时保障核心页面和互动行为的高稳定性。
- 负责提升团队研发效能，搭建全链路灰度、接口录制回放等基础能力，主导并落地了监控告警治理，全埋点数据上报，测试环境治理等项目。

### 2019.4 - 2021.4 一点资讯 内容智能部 负责人 2019 A / 2020 S
- 负责外站自媒体库，全网内容池等项目，负责爬虫整体架构和基础组件的设计。
- 基于全网内容池挖掘内容、作者的相关特征，例如高热、地域、时效性、稀缺性等，建立了一套内容、作者的分级体系，同时提供了相似度比对、原创识别等通用能力。
- 分析站内内容供给与用户消费数据，结合实验优化内容供给策略，提升内容利用率和人均时长。

### 2018.7 - 2019.4 百度视频 技术平台1部 数据研发 2018 A
- 担任长短视频、PGC 视频和机器剪辑视频业务线的数据爬取和收录工作。
- 完成视频处理流程调度的重构，提高视频处理的透明度和稳定性。
- 负责机器剪辑视频业务表字段设计、视频处理调度设计和相关功能开发。

## 项目经历
### 2023.6 - 至今 AI 长篇网文创作 发起人&负责人
背景：喜马拉雅是音频行业的 TOP1，但内容主要靠版权采买。这种模式严重限制了喜马在内容定价、内容定制以及把控市场风向方面的能力。 同时2023年 AIGC 技术的快速发展，使得新的内容创作和变现模式成为可能。
- 业内普遍采用 AI + 工具路线（作者驱动 AI ），我提出了从已有成熟的内容创作流程出发搭建 AI 产线，探索让 AI 重构创作各环节。在3个月内快速验证，解决开篇及设定设计、剧情排布、人物演绎、正文写作等难题。所在部门升级为事业部，逐渐组建了近20人的跨领域团队。
- 结合 LLM 现状，建立商业化写作理论体系，用于指导 LLM 理解爽文写作要点，如期待感、爽点、情绪缺口、信息差等，以满足读者情绪需求为导向写作。
- 从0到1设计网文 AI 创作的全流程方案，包括市场分析、爆款书拆解、故事核及开篇创作、剧情创作、正文写作等。在落地方面，主导设计了关键环节的 prompt 流，结合编辑和 AI 的能力边界，设计人机分工和工具交互。
- 目前产线 AI 参与率达90%+，产出内容质量达主流平台80-95分位，代表作品《让你管账号，你高燃混剪炸全网》番茄男频新书 TOP 6、《让你写探案，你给狄芳组CP》等。

### 2024.5 - 至今 AI 短篇网文创作 发起人&负责人
背景：在当前内容消费趋向短篇化和碎片化的市场环境下，探索利用 AI 进行短篇网文的快速创作与精准投放。尝试开拓拉新用户、内容付费和版权收入等多元化商业模式。
- 针对短篇网文市场现状，快速制定出改编创作的方案。在1个月内独立探索出古改今、性转等创作形式。并实现了创作流程的工程化，1-3w 字短篇的生产在1h 内完成，代表作品《韵灵》、《觉醒后，我和剑修仙子和离了》、《曾将爱意私藏》等。
- 协同用户增长团队，探索拉新的应用场景，已供稿 200+ 篇，衍生内容拉新 xxxxx。
- 推进自产短篇小说音频的站内付费模式实验，已供稿 600+ 篇，板块DAU xxxxx。
- 验证自产短篇小说在渠道的投放效果，形成周期数据反馈，不断打磨选题和内容质量，同时取得版权收入 xxxxx。

### 2024.4 - 至今 注意力焦点洞察 发起人&负责人
背景：在长篇、短篇网文的创作过程中，始终需保有对目标读者情绪需求的敏锐感知。我结合对网文平台和社交平台的数据，对风向、话题、人群进行深入分析，为内容创作选题、设定选择、剧情素材翻新等全链路提供指导。
- 人群分析：根据各年龄段、地域、教育程度、职业身份等维度细分出主流人群，深入了解人群需求痛点和日常关注点。为创作提供了精准的目标用户需求洞察，提升内容-人群契合度。
- 热点分析：结合网文创作长篇幅、长周期的特点，区别传统热点分析思路，不一味追求新热，提出存量声量的概念，并且形成在高存量声量的 IP/内容基础上创作的选题思路，爆款率从4%大幅提升至9%。
- 预测迭代：基于人群和热点的分析结果，通过发布短内容以及追踪内容数据，快速验证分析的正确性并迭代。

### 2021.8 - 2022.6 体育接入层升级 负责人
背景：体育后台最初是 PHP 单体应用，集中在一个高度复杂的大项目中。随着业务发展和技术架构的演进，单体应用逐渐演变为接入层的角色，存在框架老，代码乱，性能差，运营难等问题。
- 项目一期，主导体育 PHP 接入层改造方案的设计和评审，将架构分层为 API 网关，接口适配层和领域层。以体育核心接口比赛/内容底层页、推荐信息流的重构为标杆案例，沉淀出通用的代码框架和组件，并提供了从代码设计，到正确性保证，再到灰度上线的全流程指引。
- 项目二期，以网关为起点建立了全链路可观测体系。设计并落地了一整套限流，降级和全链路过载保护的服务容灾方案。
- 收益：在该方案的指引下，体育在过去的一年中重构了 106 个接口，覆盖体育 98% 的流量，核心接口 QPS 提升 1 倍+，响应时间降低 57%，实现接口告警 3min 内触发，问题定位 10min 内完成，可用性提升至 99.99%。

### 2021.8 - 2023.5 TAPISIX 网关 PMC 成员
背景：TAPISIX 是司内基于 APISIX 网关定制开发的开源协同项目。担任 PMC，负责功能开发，CR 和发展方向规划。在司内多个业务落地，多次获得公司级别奖项。
- 适配公司特殊协议，支持多种协议转换。
- 适配公司内部服务发现平台，支持动态路由能力，包括规则路由、就近路由和金丝雀路由等。
- 完备网关的降级容灾能力，实现了访问限流、故障熔断、兜底缓存、请求优先级控制等特性。
- 在体育业务落地 TAPISIX 网关，支撑日均 10 亿+ 流量，峰值 QPS 30w+，并以网关为起点建立了全链路可观测体系和容灾体系。

### 2022.6 - 2022.12 测试环境治理 负责人
背景：体育后台测试环境一直以来存在诸多痛点，严重影响了需求联调，走查和测试的效率。
- 结合网关、微服务框架、服务发现和容器平台，完成多环境泳道的方案设计与实现。支持随需求生命周期自动化管理测试环境，各特性环境可稳定运行并且流量隔离，在客户端侧可选择需求快速切换特性环境。
- 抽象体育数据资产为比赛、内容、用户互动。根据不同类型数据产生的特点，采用不同的自动化生成手段，如通过比赛数据回放模拟比赛的全流程，配置内容接入白名单同步内容至测试环境，回放正式环境互动行为接口至测试环境等，保证了测试环境数据的完备性。
- 收益：需求已全部接入，新迭代中因环境阻塞测试，出现 bug 的情况降低至 0；测试数据的构造成本从 1-2 天降低至分钟级别。

### 2020.4 - 2020.10 全网内容池 负责人
- 覆盖大内容平台、垂类 TOP、传统新闻门户在内的 30 多个站点，提供海量内容，每日更新量 5000 万+，主流站点自媒体作者覆盖率达 95%。
- 基于 Airflow 分布式调度框架，定义出细分任务和组合链路，细分任务是最小粒度的爬取目标，组合链路将细分任务灵活地拼接，满足具体的爬取需求，增加可复用性。大幅提高开发效率和可维护性。
- 针对反爬和风控，设计一系列反反爬服务和策略，如代理 IP 模块、Cookie 模块、验证码识别、手机群控和浏览器集群等，突破公众号、小红书、抖音等主流平台。
- 设计了一套海量内容存储方案，包括去重、冷热分离和动态指标拉链等。
- 爬虫系统 PaaS 平台化，针对不同场景的内容获取需求，提供个性化爬取链路配置、任务调度和内容投放能力。

### 2020.11 - 2021.4 内容和作者特征挖掘 负责人
- 支持站内外内容的点赞数、评论数等动态指标的分钟级监控。捕获竞品的 Push、热榜等信号。
- 理解内容主体和分类，挖掘内容的时效、原创、地域、稀缺度等属性，分析评论情感，多维度打分，建立全面的内容分级体系。
- 聚合作者近期的内容特征和表现数据，分析作者成长趋势，建设作者画像。挖掘出各领域的潜力作者，为自媒体平台提供价值线索。并提供创作者覆盖度、站内外表现对比等基础能力。
- 基于内容的站内外表现和基础特征，沉淀冷启池、高热池。通过长期实验，优化平台的内容供给策略，并在分发环节提供了热度信号，大幅提升内容分发效率。rctr 提升 18.4%，人均时长增加 148s。

### 2018.9 - 2018.12 机器剪辑视频 核心成员
- 独立负责机器剪辑项目中的爬取和视频处理部分，支持机器自动生产视频和辅助人工制作视频。
- 设计视频信息存储和视频的处理流程，包括长视频精彩看点、横屏短视频剪竖屏、短视频合成等类型，接入视频处理平台，日均生产 1000 余条视频。
- 负责热点事件剪辑平台项目的表设计、爬虫及后端编写，实时/离线爬取热点话题及其文字、图片和视频素材，实现辅助人工制作视频、文本转视频、自动生产热点话题视频。

## 个人评价
- **跨领域创新者**：凭借跨学科的sense、知识和实践的积累，我擅长整合内容、数据、工程、AI 等不同领域的能力，推动创新解决方案的产生。在 AI 创作、数据挖掘和内容生产等多个领域，实现了多个成功案例。
- **难题爱好者**：从不对难题 Say No，可深入分析问题和现状，快速找到切入点，提供有效的、可复用的解决方案，产出业务价值。面在网文 AI 创作、后端微服务架构改造等难题，我都取得了突破性进展。
- **团队建设与领导力**：组建并带领跨领域团队的经历，锻炼了我的领导力和团队协作能力。通过明确的业务价值和跨领域知识，激发团队成员发挥最大潜力，共同实现项目目标。